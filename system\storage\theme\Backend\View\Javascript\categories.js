/**
 * JavaScript модул за управление на категории
 * Отговаря за drag & drop функционалност, модали и филтри
 * Backup created: categories_20250703_backup.js
 */
(function() {
    'use strict';

    // Разширяване на основния модул
    document.addEventListener('DOMContentLoaded', function() {
        if (typeof BackendModule !== 'undefined') {
            BackendModule.initCategories();
        }
    });

    // Добавяне на функционалност към основния модул
    if (typeof BackendModule !== 'undefined' && typeof BackendModule === 'object') {
        Object.assign(BackendModule, {
            // Конфигурация за категории
            categories: {
                draggedElement: null,
                draggedData: null,
                dragGhost: null,
                dragPlaceholder: null,
                draggedSubcontainer: null,
                mouseMoveHandler: null,
                originalIndex: null,
                dragStartLeft: 0,
                mouseOffsetY: 0
            },

            /**
             * Инициализация на модула за категории
             */
            initCategories: function() {
                // // this.logDev && this.logDev('Initializing categories module...');

                // Проверяваме дали има категории на страницата
                const categoryItems = document.querySelectorAll('.category-item');
                // // this.logDev && this.logDev('Found category items:', categoryItems.length);

                this.setupCategoryEventListeners();
                this.initializeCategoryDragAndDrop();
                this.initializeCategoryHierarchy();
                this.initCategorySearch();

                // Закачаме веднъж event handler-ите, за да избегнем проблеми с this и премахването им
                this.boundOnMouseMove = this.onMouseMove.bind(this);
                this.boundOnMouseUp = this.onMouseUp.bind(this);

                // // this.logDev && this.logDev('Categories module initialized successfully');
            },

            /**
             * Инициализация на йерархичната функционалност
             */
            initializeCategoryHierarchy: function() {
                // // this.logDev && this.logDev('Initializing category hierarchy...');
                this.setupCategoryExpandCollapse();
                this.loadCategoryAjaxUrls();
            },

            /**
             * Зарежда AJAX URL адресите
             */
            loadCategoryAjaxUrls: function() {
                const userToken = this.getUserToken();

                // Използваме глобалните променливи от template файла
                this.categoryAjaxUrls = window.categoryAjaxUrls || {
                    loadSubcategories: window.ajaxLoadSubcategoriesUrl || 'index.php?route=catalog/category/ajax/loadSubcategories&user_token=' + userToken,
                    updateSortOrder: window.ajaxUpdateSortOrderUrl || 'index.php?route=catalog/category/ajax/updateSortOrder&user_token=' + userToken,
                    getCategoryInfo: window.ajaxGetCategoryInfoUrl || 'index.php?route=catalog/category/ajax/getCategoryInfo&user_token=' + userToken,
                    deleteCategory: window.ajaxDeleteCategoryUrl || 'index.php?route=catalog/category/delete&user_token=' + userToken
                };

                // // this.logDev && this.logDev('Category AJAX URLs loaded:', this.categoryAjaxUrls);
            },

            /**
             * Настройва expand/collapse функционалност
             */
            setupCategoryExpandCollapse: function() {
                // Премахваме старите event listeners, за да избегнем дублиране
                document.querySelectorAll('.category-expand-btn').forEach(btn => {
                    const newBtn = btn.cloneNode(true);
                    btn.parentNode.replaceChild(newBtn, btn);
                });

                document.querySelectorAll('.category-expand-btn').forEach(button => {
                    button.addEventListener('click', (e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        const categoryId = button.dataset.categoryId;
                        if (categoryId) {
                            this.toggleCategoryExpand(button, categoryId);
                        }
                    });
                });
            },

            /**
             * Проверява дали категорията има подкатегории
             */
            categoryHasSubcategories: function(categoryRow) {
                // Проверяваме за data атрибут или клас, който показва наличие на подкатегории
                return categoryRow.dataset.hasSubcategories === 'true' ||
                       categoryRow.classList.contains('has-subcategories');
            },

            /**
             * Добавя expand/collapse бутон към категория
             */
            addCategoryExpandButton: function(categoryRow, categoryId) {
                const expandButton = document.createElement('button');
                expandButton.className = 'category-expand-btn w-6 h-6 flex items-center justify-center text-gray-500 hover:text-gray-700 mr-2 transition-colors';
                expandButton.innerHTML = '<i class="ri-arrow-right-s-line"></i>';
                expandButton.dataset.categoryId = categoryId;
                expandButton.dataset.expanded = 'false';
                expandButton.title = 'Покажи подкатегории';

                // Добавяме бутона в началото на flex контейнера
                const flexContainer = categoryRow.querySelector('.flex-1');
                if (flexContainer) {
                    flexContainer.insertBefore(expandButton, flexContainer.firstChild);
                }

                // Event listener за expand/collapse
                expandButton.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    this.toggleCategoryExpand(expandButton, categoryId);
                });
            },

            /**
             * Превключва expand/collapse състоянието на категория
             */
            toggleCategoryExpand: function(button, categoryId) {
                const isExpanded = button.dataset.expanded === 'true';

                if (isExpanded) {
                    this.collapseCategorySubcategories(button, categoryId);
                } else {
                    this.expandCategorySubcategories(button, categoryId);
                }
            },

            /**
             * Разширява категорията и зарежда подкатегориите
             */
            expandCategorySubcategories: function(button, categoryId) {
                // // this.logDev && this.logDev('Expanding category:', categoryId);

                // Визуални промени
                button.dataset.expanded = 'true';
                button.querySelector('i').className = 'ri-arrow-down-s-line';
                button.title = 'Скрий подкатегории';

                // Показваме loading индикатор
                this.showCategoryLoading(button);

                // AJAX заявка за зареждане на подкатегориите
                this.loadCategorySubcategories(categoryId)
                    .then(subcategories => {
                        this.hideCategoryLoading(button);
                        this.renderCategorySubcategories(button, categoryId, subcategories);
                    })
                    .catch(error => {
                        this.hideCategoryLoading(button);
                        // // this.logDev && this.logDev('Error loading subcategories:', error);
                        this.showNotification('Грешка при зареждане на подкатегориите', 'error');
                    });
            },

            /**
             * Свива категорията и скрива подкатегориите
             */
            collapseCategorySubcategories: function(button, categoryId) {
                // // this.logDev && this.logDev('Collapsing category:', categoryId);

                // Визуални промени
                button.dataset.expanded = 'false';
                button.querySelector('i').className = 'ri-arrow-right-s-line';
                button.title = 'Покажи подкатегории';

                const container = document.querySelector(`.subcategories-container[data-parent-id="${categoryId}"]`);
                if (container) {
                    // Анимираме свиването и СЛЕД това премахваме елемента и обновяваме височината
                    this.animateCategoryCollapse(container, () => {
                        container.remove();
                        this.updateParentContainersHeight(button);
                    });
                }
            },

            /**
             * Зарежда подкатегориите чрез AJAX
             */
            loadCategorySubcategories: function(parentId) {
                const url = this.categoryAjaxUrls.loadSubcategories;
                const formData = new FormData();
                formData.append('parent_id', parentId);
                
                return fetch(url, {
                    method: 'POST',
                    body: formData,
                    headers: { 'X-Requested-With': 'XMLHttpRequest' }
                })
                .then(response => response.ok ? response.json() : Promise.reject(`HTTP error! status: ${response.status}`))
                .then(data => data.success ? data.subcategories || [] : Promise.reject(data.error || 'Неизвестна грешка'));
            },

            /**
             * Рендерира подкатегориите в DOM
             */
            renderCategorySubcategories: function(button, parentId, subcategories) {
                const parentRow = button.closest('.category-item');
                if (!parentRow) return;

                // Премахване на стар контейнер, ако съществува
                this.removeCategorySubcategories(parentId); 
                
                const subcategoriesContainer = document.createElement('div');
                subcategoriesContainer.className = 'subcategories-container';
                subcategoriesContainer.dataset.parentId = parentId;

                const parentLevel = parseInt(parentRow.dataset.level || '0');

                subcategories.forEach(subcategory => {
                    const subcategoryRow = this.createCategorySubcategoryRow(subcategory, parentId, parentLevel + 1);
                    subcategoriesContainer.appendChild(subcategoryRow);
                });

                parentRow.after(subcategoriesContainer);
                this.animateCategoryExpand(subcategoriesContainer);

                // Актуализираме височината на родителските контейнери
                this.updateParentContainersHeight(subcategoriesContainer);

                this.initializeCategoryDragAndDrop(); // Re-initialize D&D for new elements
                this.setupCategoryExpandCollapse(); // Re-initialize expand/collapse for new elements
            },

            /**
             * Създава ред за подкатегория
             */
            createCategorySubcategoryRow: function(subcategory, parentId, level) {
                const row = document.createElement('div');
                row.className = 'category-item subcategory-item';
                row.dataset.categoryId = subcategory.category_id;
                row.dataset.parentId = parentId;
                row.dataset.sortOrder = subcategory.sort_order || 0;
                row.dataset.level = level;
                if (subcategory.has_subcategories) {
                    row.dataset.hasSubcategories = 'true';
                }
            
                const indentWidth = level * 30; // 30px отстъп на ниво
            
                row.innerHTML = `
                    <div class="category-inner-wrapper" style="margin-left: ${indentWidth}px;">
                        <div class="drag-handle">
                            <i class="ri-drag-move-2-line"></i>
                        </div>
            
                        
                        ${subcategory.has_subcategories ?
                            `<div class="expand-handle"><button class="category-expand-btn" data-category-id="${subcategory.category_id}" data-expanded="false" title="Покажи подкатегории">
                                <i class="ri-arrow-right-s-line"></i>
                            </button></div>` :
                            ''
                        }
            
                        <div class="category-content">
                            <div class="category-title">
                                <h3 class="font-medium">${subcategory.name}</h3>
                                <span class="status-badge ${subcategory.status_class}">${subcategory.status_text}</span>
                            </div>
                            <div class="category-meta">
                                <span>${subcategory.product_count} продукта</span>
                                ${subcategory.has_subcategories ? `<span>• ${subcategory.subcategory_count || 0} подкатегории</span>` : ''}
                                <a href="${subcategory.products_url || '#'}" class="link">Виж продуктите</a>
                            </div>
                        </div>
            
                        <div class="category-actions">
                            <button class="add-subcategory-btn w-8 h-8 flex items-center justify-center text-green-600 hover:bg-green-50 rounded-full" title="Добави подкатегория">
                                <div class="w-5 h-5 flex items-center justify-center">
                                    <i class="ri-add-line"></i>
                                </div>
                            </button>
                            <a href="${subcategory.edit}" class="action-btn" title="Редактиране"><i class="ri-edit-line"></i></a>
                            <button class="category-delete-btn action-btn-danger" title="Изтриване"
                                    data-category-id="${subcategory.category_id}"
                                    data-category-name="${subcategory.name}">
                                <i class="ri-delete-bin-line"></i>
                            </button>
                        </div>
                    </div>
                `;
                return row;
            },

            /**
             * Настройка на всички event listeners за категории
             */
            setupCategoryEventListeners: function() {
                this.setupCategoryFilterModal();
                this.setupCategoryModal();
                this.setupCategoryDropdowns();
                this.setupAddSubcategoryButtons();
                this.setupCategoryDeleteButtons();
            },

            /**
             * Премахва подкатегориите от DOM
             */
            removeCategorySubcategories: function(parentId) {
                const container = document.querySelector(`.subcategories-container[data-parent-id="${parentId}"]`);
                if (container) {
                    // При почистване преди ново рендиране, просто премахваме елемента без анимация и без ъпдейт на височина.
                    container.remove();
                }
            },

            /**
             * Рекурсивно обновява височината на родителските контейнери.
             * @param {HTMLElement} element - Елементът, който задейства обновяването.
             */
            updateParentContainersHeight: function(element) {
                const parentContainer = element.closest('.subcategories-container');
                if (!parentContainer) {
                    return;
                }

                // Изчакваме CSS анимацията да приключи (300ms), за да гарантираме,
                // че scrollHeight е изчислена коректно.
                setTimeout(() => {
                    requestAnimationFrame(() => {
                        // Проверяваме дали контейнерът все още е в DOM, преди да го манипулираме
                        if (document.body.contains(parentContainer)) {
                            parentContainer.style.maxHeight = parentContainer.scrollHeight + 'px';
                        }
                    });
                }, 300);

                // Продължаваме рекурсивно нагоре по веригата
                const ownerCategoryId = parentContainer.dataset.parentId;
                if (ownerCategoryId) {
                    const ownerCategoryItem = document.querySelector(`.category-item[data-category-id="${ownerCategoryId}"]`);
                    if (ownerCategoryItem) {
                        this.updateParentContainersHeight(ownerCategoryItem);
                    }
                }
            },

            // --- Анимации и помощни функции (остават предимно същите) ---
            animateCategoryExpand: function(container) {
                requestAnimationFrame(() => {
                    container.style.maxHeight = container.scrollHeight + 'px';
                    container.style.opacity = '1';
                });
            },
            animateCategoryCollapse: function(container, callback) {
                container.style.maxHeight = '0';
                container.style.opacity = '0';
                setTimeout(() => callback && callback(), 300);
            },
            showNotification: function(message, type = 'info') {
                if (typeof this.showAlert === 'function') {
                    this.showAlert(type, message);
                }
            },

            /**
             * Показва loading индикатор за категория
             */
            showCategoryLoading: function(button) {
                const icon = button.querySelector('i');
                if (icon) {
                    icon.className = 'ri-loader-4-line animate-spin';
                }
            },

            /**
             * Скрива loading индикатора за категория
             */
            hideCategoryLoading: function(button) {
                const icon = button.querySelector('i');
                if (icon && button.dataset.expanded === 'true') {
                    icon.className = 'ri-arrow-down-s-line';
                } else if (icon) {
                    icon.className = 'ri-arrow-right-s-line';
                }
            },

            

            /**
             * Настройка на филтър модала за категории
             */
            setupCategoryFilterModal: function() {
                const filterBtn = document.getElementById('filter-btn');
                const filterModal = document.getElementById('filter-modal');
                const closeFilter = document.getElementById('close-filter');
                const filterForm = document.getElementById('filter-form');
                const resetFilter = document.getElementById('reset-filter');

                if (filterBtn && filterModal) {
                    filterBtn.addEventListener('click', () => {
                        this.openCategoryModal(filterModal);
                    });
                }

                if (closeFilter && filterModal) {
                    closeFilter.addEventListener('click', () => {
                        this.closeCategoryModal(filterModal);
                    });
                }

                if (filterModal) {
                    filterModal.addEventListener('click', (e) => {
                        if (e.target === filterModal) {
                            this.closeCategoryModal(filterModal);
                        }
                    });
                }

                if (filterForm) {
                    filterForm.addEventListener('submit', (e) => {
                        e.preventDefault();
                        this.handleCategoryFilterSubmit(filterForm);
                        if (filterModal) {
                            this.closeCategoryModal(filterModal);
                        }
                    });
                }

                if (resetFilter && filterForm) {
                    resetFilter.addEventListener('click', () => {
                        filterForm.reset();
                        this.logDev && this.logDev('Filter form reset');
                    });
                }
            },

            /**
             * Настройка на категория модала
             */
            setupCategoryModal: function() {
                const addCategoryBtn = document.getElementById('add-category-btn');
                const categoryModal = document.getElementById('category-modal');
                const closeCategoryModal = document.getElementById('close-category-modal');
                const cancelCategory = document.getElementById('cancel-category');
                const categoryForm = document.getElementById('category-form');
                const categoryModalTitle = document.getElementById('category-modal-title');
                const editButtons = document.querySelectorAll('.ri-edit-line');

                if (addCategoryBtn && categoryModal) {
                    addCategoryBtn.addEventListener('click', () => {
                        if (categoryModalTitle) {
                            categoryModalTitle.textContent = 'Добавяне на категория';
                        }
                        this.openCategoryModal(categoryModal);
                    });
                }

                if (closeCategoryModal && categoryModal) {
                    closeCategoryModal.addEventListener('click', () => {
                        this.closeCategoryModal(categoryModal);
                    });
                }

                if (cancelCategory && categoryModal) {
                    cancelCategory.addEventListener('click', () => {
                        this.closeCategoryModal(categoryModal);
                    });
                }

                if (categoryModal) {
                    categoryModal.addEventListener('click', (e) => {
                        if (e.target === categoryModal) {
                            this.closeCategoryModal(categoryModal);
                        }
                    });
                }

                if (categoryForm) {
                    categoryForm.addEventListener('submit', (e) => {
                        e.preventDefault();
                        this.handleCategorySubmit(categoryForm);
                        if (categoryModal) {
                            this.closeCategoryModal(categoryModal);
                        }
                    });
                }

                // Setup edit buttons
                // editButtons.forEach(btn => {
                //     const editLink = btn.closest('a');
                //     if (editLink) {
                //         editLink.addEventListener('click', (e) => {
                //             e.preventDefault();
                //             if (categoryModalTitle) {
                //                 categoryModalTitle.textContent = 'Редактиране на категория';
                //             }
                //             if (categoryModal) {
                //                 this.openCategoryModal(categoryModal);
                //             }
                //         });
                //     }
                // });
            },

            /**
             * Настройка на dropdown менютата за категории
             */
            setupCategoryDropdowns: function() {
                const dropdownButtons = document.querySelectorAll('[data-dropdown-toggle]');
                
                dropdownButtons.forEach(button => {
                    button.addEventListener('click', (e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        
                        const targetId = button.getAttribute('data-dropdown-toggle');
                        const dropdown = document.getElementById(targetId);
                        
                        if (dropdown) {
                            // Затваряне на други dropdown менюта
                            document.querySelectorAll('.dropdown-menu').forEach(menu => {
                                if (menu !== dropdown) {
                                    menu.classList.add('hidden');
                                }
                            });
                            
                            // Toggle на текущото dropdown меню
                            dropdown.classList.toggle('hidden');
                        }
                    });
                });

                // Затваряне на dropdown менютата при клик извън тях
                document.addEventListener('click', () => {
                    document.querySelectorAll('.dropdown-menu').forEach(menu => {
                        menu.classList.add('hidden');
                    });
                });
            },

            /**
             * Настройка на бутоните за добавяне на подкатегории
             */
            setupAddSubcategoryButtons: function() {
                // Използваме event delegation за динамично добавени елементи
                document.addEventListener('click', (e) => {
                    if (e.target.closest('.add-subcategory-btn')) {
                        e.preventDefault();
                        e.stopPropagation();

                        const button = e.target.closest('.add-subcategory-btn');
                        const categoryItem = button.closest('.category-item');

                        if (categoryItem) {
                            const parentCategoryId = categoryItem.dataset.categoryId;

                            if (parentCategoryId) {
                                this.openAddSubcategoryWindow(parentCategoryId);
                            } else {
                                this.showNotification('Грешка: Не може да се намери ID на родителската категория', 'error');
                            }
                        }
                    }
                });
            },

            /**
             * Отваря прозорец за добавяне на подкатегория
             */
            openAddSubcategoryWindow: function(parentCategoryId) {
                const userToken = this.getUserToken();
                const addCategoryUrl = `index.php?route=catalog/category/add&user_token=${userToken}&parent_category_id=${parentCategoryId}`;

                // Отваряме нов прозорец
                window.open(addCategoryUrl, '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');
            },

            /**
             * Настройка на бутоните за изтриване на категории
             */
            setupCategoryDeleteButtons: function() {
                // Използваме event delegation за динамично добавени елементи
                document.addEventListener('click', (e) => {
                    if (e.target.closest('.category-delete-btn')) {
                        e.preventDefault();
                        e.stopPropagation();

                        const button = e.target.closest('.category-delete-btn');
                        const categoryId = button.dataset.categoryId;
                        const categoryName = button.dataset.categoryName;

                        if (categoryId) {
                            this.handleCategoryDelete(categoryId, categoryName);
                        } else {
                            this.showNotification('Грешка: Не може да се намери ID на категорията', 'error');
                        }
                    }
                });
            },

            /**
             * Обработва изтриването на категория
             */
            handleCategoryDelete: function(categoryId, categoryName) {
                // Показваме диалога за потвърждение
                this.showDeleteConfirmationDialog(categoryId, categoryName, (confirmed) => {
                    if (confirmed) {
                        // Потребителят потвърди изтриването
                        this.deleteCategoryAjax(categoryId)
                            .then(() => {
                                // Успешно изтриване - нищо допълнително не е нужно,
                                // deleteCategoryAjax вече обработва успеха
                            })
                            .catch((error) => {
                                // Грешката вече е обработена в deleteCategoryAjax
                                console.error('Category deletion failed:', error);
                            });
                    }
                });
            },

            /**
             * Инициализира drag & drop функционалността.
             */
            initializeCategoryDragAndDrop: function() {
                // Премахваме стари слушатели, ако има такива, за да не се дублират
                document.removeEventListener('mousedown', this.onMouseDown.bind(this));

                // Използваме event delegation, за да работи и за динамично добавени елементи
                document.addEventListener('mousedown', this.onMouseDown.bind(this));
            },

            /**
             * Прихваща натискане на мишката върху drag дръжката.
             */
            onMouseDown: function(e) {
                if (this.categories.draggedElement || e.button !== 0 || !e.target.closest('.drag-handle')) {
                    return;
                }
                e.preventDefault();
                e.stopPropagation();

                this.categories.draggedElement = e.target.closest('.category-item');
                if (!this.categories.draggedElement) return;

                // Намираме и запазваме контейнера с подкатегории, ако съществува и е разширен
                const subcontainer = this.categories.draggedElement.nextElementSibling;
                if (subcontainer && subcontainer.classList.contains('subcategories-container')) {
                    this.categories.draggedSubcontainer = subcontainer;
                }

                const parentContainer = this.categories.draggedElement.parentElement;
                parentContainer.classList.add('is-drag-container');

                this.categories.originalIndex = Array.from(parentContainer.children)
                    .filter(child => child.classList.contains('category-item'))
                    .indexOf(this.categories.draggedElement);

                this.prepareElementForDrag(e);

                document.addEventListener('mousemove', this.boundOnMouseMove);
                document.addEventListener('mouseup', this.boundOnMouseUp);
            },

            prepareElementForDrag: function(e) {
                const element = this.categories.draggedElement;
                const rect = element.getBoundingClientRect();

                // Изчисляваме общата височина, включително подкатегориите
                let totalHeight = rect.height;
                if (this.categories.draggedSubcontainer) {
                    totalHeight += this.categories.draggedSubcontainer.getBoundingClientRect().height;
                }

                this.categories.dragPlaceholder = document.createElement('div');
                this.categories.dragPlaceholder.id = 'category-drag-placeholder';
                this.categories.dragPlaceholder.style.height = `${totalHeight}px`;
                const elementStyle = window.getComputedStyle(element);
                this.categories.dragPlaceholder.style.marginTop = elementStyle.marginTop;
                this.categories.dragPlaceholder.style.marginBottom = elementStyle.marginBottom;
                element.after(this.categories.dragPlaceholder);

                // Скриваме оригиналния контейнер с подкатегории по време на влаченето
                if (this.categories.draggedSubcontainer) {
                    this.categories.draggedSubcontainer.style.display = 'none';
                }

                element.style.position = 'absolute';
                element.style.top = `${element.offsetTop}px`;
                element.style.left = `${element.offsetLeft}px`;
                element.style.width = `${rect.width}px`;
                element.classList.add('is-dragging');

                this.categories.mouseOffsetY = e.clientY - rect.top;
            },

            onMouseMove: function(e) {
                const element = this.categories.draggedElement;
                if (!element) return;

                const parentContainer = this.categories.dragPlaceholder.parentElement;
                if (!parentContainer) return;

                const parentRect = parentContainer.getBoundingClientRect();
                const elementRect = element.getBoundingClientRect();
                
                // Изчисляваме новата горна позиция спрямо родителския контейнер
                let newTop = e.clientY - parentRect.top - this.categories.mouseOffsetY;

                // Ограничаваме движението в рамките на родителския контейнер
                if (newTop < 0) {
                    newTop = 0;
                } else if (newTop + elementRect.height > parentRect.height) {
                    newTop = parentRect.height - elementRect.height;
                }
                
                element.style.top = `${newTop}px`;

                this.checkAndSwapElements();
            },

            onMouseUp: function() {
                const element = this.categories.draggedElement;
                if (!element) return;

                const parentContainer = element.parentElement;
                if (parentContainer) {
                    parentContainer.classList.remove('is-drag-container');
                }
                element.classList.remove('is-dragging');
                element.removeAttribute('style');

                document.removeEventListener('mousemove', this.boundOnMouseMove);
                document.removeEventListener('mouseup', this.boundOnMouseUp);

                // Връщаме елемента и неговите подкатегории на мястото на плейсхолдъра
                if (this.categories.dragPlaceholder && this.categories.dragPlaceholder.parentNode) {
                    const placeholder = this.categories.dragPlaceholder;
                    // Вмъкваме основния елемент преди плейсхолдъра
                    placeholder.parentNode.insertBefore(element, placeholder);
                    // Ако има контейнер с подкатегории, вмъкваме го след основния елемент и го показваме
                    if (this.categories.draggedSubcontainer) {
                        element.after(this.categories.draggedSubcontainer);
                        this.categories.draggedSubcontainer.style.display = '';
                    }
                    // Премахваме плейсхолдъра
                    placeholder.remove();
                }

                const newIndex = Array.from(parentContainer.children)
                    .filter(child => child.classList.contains('category-item'))
                    .indexOf(element);

                if (this.categories.originalIndex !== newIndex) {
                    const categoryId = element.dataset.categoryId;
                    const parentId = element.dataset.parentId;
                    this.updateSortOrderForSiblings(parentContainer, parentId);
                }

                this.categories.draggedElement = null;
                this.categories.dragPlaceholder = null;
                this.categories.draggedSubcontainer = null; // Нулираме и контейнера
                this.categories.originalIndex = -1;
                this.categories.mouseOffsetY = 0;
            },

            /**
             * Проверява дали влаченият елемент трябва да се размени с някой от съседните.
             */
            checkAndSwapElements: function() {
                const placeholder = this.categories.dragPlaceholder;
                const element = this.categories.draggedElement;
                if (!element || !placeholder) return;

                const elementRect = element.getBoundingClientRect();
                const parentId = element.dataset.parentId;

                // Ако елементът няма parentId, не можем да продължим.
                if (typeof parentId === 'undefined') {
                    return;
                }

                const parentContainer = placeholder.parentElement;
                if (!parentContainer) return;

                // Намираме всички "братя" (елементи в същия контейнер и със същия parent_id).
                const siblings = Array.from(parentContainer.children).filter(
                    child => child.classList.contains('category-item') &&
                             child !== element &&
                             child.dataset.parentId === parentId
                );
                
                for (const sibling of siblings) {
                    const siblingRect = sibling.getBoundingClientRect();
                    const isDraggingDown = placeholder.offsetTop < sibling.offsetTop;

                    // Ако влачим надолу и сме минали средата на съседния елемент.
                    if (isDraggingDown && elementRect.bottom > siblingRect.top + siblingRect.height / 2) {
                        sibling.after(placeholder);
                        return; // Прекратяваме, за да не се разменя с няколко наведнъж.
                    }
                    
                    // Ако влачим нагоре и сме минали средата на съседния елемент.
                    if (!isDraggingDown && elementRect.top < siblingRect.bottom - siblingRect.height / 2) {
                        sibling.before(placeholder);
                        return;
                    }
                }
            },

            /**
             * Приключва влаченето, връща елемента на мястото му и записва промените.
             */




            /**
             * Обновява sort order на база DOM позиция и изпраща към сървъра
             */
            updateSortOrderForSiblings(container, parentId) {
                const updatedCategories = [];
                const children = Array.from(container.children).filter(c => c.matches('.category-item, .subcategories-container'));
                
                let currentSortOrder = 1;
                children.forEach(child => {
                    if(child.classList.contains('category-item')) {
                         const categoryId = child.dataset.categoryId;
                         // Актуализираме parentId и sortOrder в dataset-а
                         child.dataset.parentId = parentId;
                         child.dataset.sortOrder = currentSortOrder;
                         
                         updatedCategories.push({
                             category_id: categoryId,
                             parent_id: parentId,
                             sort_order: currentSortOrder
                         });
                         currentSortOrder++;
                    }
                });

                // this.logDev && this.logDev('Updating sort order for:', updatedCategories);
                
                // Показваме loading
                this.showCategoryMoveLoading();
                
                this.updateCategorySortOrderRequest({ categories: updatedCategories })
                    .then(response => {
                        if (response.success) {
                            this.showNotification('Позициите на категориите са обновени успешно', 'success');
                            // По желание: презареждане на страницата за пълна синхронизация
                            // window.location.reload();
                        } else {
                            throw new Error(response.error || 'Неизвестна грешка от сървъра');
                        }
                    })
                    .catch(error => {
                        this.logDev && this.logDev('Error updating sort order:', error);
                        this.showNotification('Грешка при обновяване на позициите: ' + error.message, 'error');
                        // В случай на грешка, презареждаме, за да върнем старото състояние
                        // window.location.reload(); 
                    })
                    .finally(() => {
                        this.hideCategoryMoveLoading();
                    });
            },

            /**
             * AJAX заявка за обновяване на sort order
             */
            updateCategorySortOrderRequest: function(data) {
                if (!this.categoryAjaxUrls || !this.categoryAjaxUrls.updateSortOrder) {
                    return Promise.reject(new Error('AJAX URL за обновяване на sort order не е зададен'));
                }
                const url = this.categoryAjaxUrls.updateSortOrder;

                return fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify(data)
                })
                .then(response => {
                    if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
                    return response.json();
                });
            },

            /**
             * Получава ID на категорията от DOM елемент
             */
            getCategoryId: function(element) {
                if (!element) {
                    this.logDev && this.logDev('getCategoryId: element is null/undefined');
                    return null;
                }

                return element.dataset.categoryId ||
                       element.querySelector('[data-category-id]')?.dataset.categoryId ||
                       null;
            },

            /**
             * Получава parent ID на категорията от DOM елемент
             */
            getCategoryParentId: function(element) {
                if (!element) {
                    this.logDev && this.logDev('getCategoryParentId: element is null/undefined');
                    return '0';
                }
                return element.dataset.parentId || '0';
            },

            /**
             * Получава нивото на категорията от DOM елемент
             */
            getCategoryLevel: function(element) {
                if (!element) {
                    this.logDev && this.logDev('getCategoryLevel: element is null/undefined');
                    return 0;
                }
                return parseInt(element.dataset.level || '0');
            },

            /**
             * Получава името на категорията от DOM елемент
             */
            getCategoryName: function(element) {
                if (!element) {
                    this.logDev && this.logDev('getCategoryName: element is null/undefined');
                    return '';
                }
                const nameElement = element.querySelector('.font-medium');
                return nameElement ? nameElement.textContent.trim() : '';
            },

   
            /**
             * Събира данни за всички подкатегории на същото ниво (siblings)
             */
            getSiblingCategoriesData: function(parentId) {
                const siblings = [];

                // Намираме всички категории със същия parent_id
                const categoryElements = document.querySelectorAll('.category-item');

                categoryElements.forEach(element => {
                    const elementParentId = this.getCategoryParentId(element);

                    // Проверяваме дали е на същото ниво
                    if (elementParentId == parentId) {
                        const categoryData = {
                            category_id: this.getCategoryId(element),
                            parent_id: elementParentId,
                            sort_order: parseInt(element.dataset.sortOrder || 0),
                            level: parseInt(element.dataset.level || 0),
                            name: this.getCategoryName(element)
                        };

                        siblings.push(categoryData);
                    }
                });

                // Сортираме по текущия sort_order за правилна последователност
                siblings.sort((a, b) => a.sort_order - b.sort_order);

                this.logDev && this.logDev('Found sibling categories:', siblings);

                return siblings;
            },

            /**
             * AJAX заявка за обновяване на sort order
             */
            updateCategorySortOrder: function(data) {
                if (!this.categoryAjaxUrls || !this.categoryAjaxUrls.updateSortOrder) {
                    return Promise.reject(new Error('AJAX URL за обновяване на sort order не е зададен'));
                }

                const url = this.categoryAjaxUrls.updateSortOrder;


                return fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify(data)
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .catch(error => {
                    this.logDev && this.logDev('updateCategorySortOrder error:', error);
                    throw error;
                });
            },

            /**
             * Показва loading за move операция
             */
            showCategoryMoveLoading: function() {
                // Създаваме loading overlay
                const overlay = document.createElement('div');
                overlay.id = 'category-move-loading';
                overlay.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
                overlay.innerHTML = `
                    <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
                        <i class="ri-loader-4-line animate-spin text-2xl text-blue-600"></i>
                        <span class="text-gray-700">Преместване на категория...</span>
                    </div>
                `;
                document.body.appendChild(overlay);
                // this.logDev && this.logDev('Showing move loading...');
            },

            /**
             * Скрива loading за move операция
             */
            hideCategoryMoveLoading: function() {
                const overlay = document.getElementById('category-move-loading');
                if (overlay) {
                    overlay.remove();
                }
                // this.logDev && this.logDev('Hiding move loading...');
            },

            /**
             * Обновява списъка с категории
             */
            refreshCategoryList: function() {
                // Презареждаме страницата за да видим промените
                // window.location.reload();
            },

            /**
             * Скрива всички drop индикатори
             */
            hideAllCategoryDropIndicators: function() {
                const indicators = document.querySelectorAll('.drop-indicator');
                indicators.forEach(indicator => indicator.remove());

                const dropZones = document.querySelectorAll('.drop-zone-active');
                dropZones.forEach(zone => zone.classList.remove('drop-zone-active'));
            },



            /**
             * Обработка на филтър форма
             */
            handleCategoryFilterSubmit: function(form) {
                const formData = new FormData(form);
                const params = new URLSearchParams();

                for (let [key, value] of formData.entries()) {
                    if (value.trim() !== '') {
                        params.append(key, value);
                    }
                }

                // Добавяне на user_token
                if (this.config.userToken) {
                    params.append('user_token', this.config.userToken);
                }

                const url = form.action + '?' + params.toString();
                window.location.href = url;
            },

            /**
             * Обработка на категория форма
             */
            handleCategorySubmit: function(form) {
                const formData = new FormData(form);

                fetch(form.action, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        window.location.reload();
                    } else {
                        alert(data.error || 'Възникна грешка при запазването.');
                    }
                })
                .catch(error => {
                    console.error('Form submission error:', error);
                    alert('Възникна грешка при изпращането на формата.');
                });
            },

            /**
             * Отваряне на модал за категории
             */
            openCategoryModal: function(modal) {
                if (modal) {
                    modal.classList.remove('hidden');
                    modal.classList.add('flex');
                    document.body.style.overflow = 'hidden';
                }
            },

            /**
             * Затваряне на модал за категории
             */
            closeCategoryModal: function(modal) {
                if (modal) {
                    modal.classList.add('hidden');
                    modal.classList.remove('flex');
                    document.body.style.overflow = '';
                }
            },

            /**
             * Показва диалог за потвърждение на изтриване на категория
             */
            showDeleteConfirmationDialog: function(categoryId, categoryName, callback) {
                // Премахваме съществуващ диалог, ако има такъв
                const existingModal = document.getElementById('category-delete-modal');
                if (existingModal) {
                    existingModal.remove();
                }

                // Създаваме модалния диалог
                const modal = document.createElement('div');
                modal.id = 'category-delete-modal';
                modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';

                modal.innerHTML = `
                    <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
                        <div class="p-6">
                            <div class="flex items-center mb-4">
                                <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mr-4">
                                    <i class="ri-delete-bin-line text-red-600 text-xl"></i>
                                </div>
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900">Потвърждение за изтриване</h3>
                                    <p class="text-sm text-gray-600">Това действие не може да бъде отменено</p>
                                </div>
                            </div>

                            <div class="mb-6">
                                <p class="text-gray-700">
                                    Сигурни ли сте, че искате да изтриете категорията
                                    <strong>"${categoryName || 'тази категория'}"</strong>?
                                </p>
                            </div>

                            <div class="flex justify-end space-x-3">
                                <button id="delete-cancel-btn" class="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400 transition-colors">
                                    Не
                                </button>
                                <button id="delete-confirm-btn" class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors">
                                    Да
                                </button>
                            </div>
                        </div>
                    </div>
                `;

                // Добавяме модала към DOM
                document.body.appendChild(modal);

                // Фокусираме бутона "Не" по подразбиране
                const cancelBtn = modal.querySelector('#delete-cancel-btn');
                const confirmBtn = modal.querySelector('#delete-confirm-btn');

                setTimeout(() => {
                    cancelBtn.focus();
                }, 100);

                // Обработчици на събития
                const handleCancel = () => {
                    modal.remove();
                    if (callback) callback(false);
                };

                const handleConfirm = () => {
                    modal.remove();
                    if (callback) callback(true);
                };

                // Event listeners
                cancelBtn.addEventListener('click', handleCancel);
                confirmBtn.addEventListener('click', handleConfirm);

                // Затваряне при клик извън модала
                modal.addEventListener('click', (e) => {
                    if (e.target === modal) {
                        handleCancel();
                    }
                });

                // Keyboard navigation
                modal.addEventListener('keydown', (e) => {
                    if (e.key === 'Escape') {
                        e.preventDefault();
                        handleCancel();
                    } else if (e.key === 'Enter') {
                        e.preventDefault();
                        handleConfirm();
                    } else if (e.key === 'Tab') {
                        e.preventDefault();
                        // Toggle focus between buttons
                        if (document.activeElement === cancelBtn) {
                            confirmBtn.focus();
                        } else {
                            cancelBtn.focus();
                        }
                    }
                });
            },

            /**
             * Скрива диалога за потвърждение на изтриване
             */
            hideDeleteConfirmationDialog: function() {
                const modal = document.getElementById('category-delete-modal');
                if (modal) {
                    modal.remove();
                }
            },

            /**
             * Изтрива категория чрез AJAX
             */
            deleteCategoryAjax: function(categoryId) {
                if (!categoryId) {
                    this.showNotification('Невалиден ID на категория', 'error');
                    return Promise.reject(new Error('Невалиден ID на категория'));
                }

                if (!this.categoryAjaxUrls || !this.categoryAjaxUrls.deleteCategory) {
                    this.showNotification('AJAX URL за изтриване не е зададен', 'error');
                    return Promise.reject(new Error('AJAX URL за изтриване не е зададен'));
                }

                // Показваме loading индикатор
                this.showCategoryDeleteLoading();

                const url = this.categoryAjaxUrls.deleteCategory;
                const formData = new FormData();
                formData.append('category_id', categoryId);

                return fetch(url, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    this.hideCategoryDeleteLoading();

                    if (data.success) {
                        this.showNotification(data.success, 'success');

                        // Премахваме категорията от DOM
                        this.removeCategoryFromDOM(categoryId);

                        // Ако има redirect URL, можем да го използваме за презареждане
                        if (data.redirect) {
                            setTimeout(() => {
                                window.location.reload();
                            }, 1500);
                        }

                        return data;
                    } else {
                        throw new Error(data.error || 'Неизвестна грешка при изтриване');
                    }
                })
                .catch(error => {
                    this.hideCategoryDeleteLoading();
                    const errorMessage = error.message || 'Грешка при изтриване на категорията';
                    this.showNotification(errorMessage, 'error');
                    throw error;
                });
            },

            /**
             * Премахва категорията от DOM след успешно изтриване
             */
            removeCategoryFromDOM: function(categoryId) {
                const categoryElement = document.querySelector(`.category-item[data-category-id="${categoryId}"]`);
                if (categoryElement) {
                    // Премахваме и контейнера с подкатегории, ако съществува
                    const subcategoriesContainer = categoryElement.nextElementSibling;
                    if (subcategoriesContainer && subcategoriesContainer.classList.contains('subcategories-container')) {
                        subcategoriesContainer.remove();
                    }

                    // Премахваме основния елемент
                    categoryElement.remove();
                }
            },

            /**
             * Показва loading индикатор за изтриване
             */
            showCategoryDeleteLoading: function() {
                const overlay = document.createElement('div');
                overlay.id = 'category-delete-loading';
                overlay.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
                overlay.innerHTML = `
                    <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
                        <i class="ri-loader-4-line animate-spin text-2xl text-red-600"></i>
                        <span class="text-gray-700">Изтриване на категория...</span>
                    </div>
                `;
                document.body.appendChild(overlay);
            },

            /**
             * Скрива loading индикатора за изтриване
             */
            hideCategoryDeleteLoading: function() {
                const overlay = document.getElementById('category-delete-loading');
                if (overlay) {
                    overlay.remove();
                }
            },

            /**
             * Инициализация на търсенето на категории
             * Адаптиран от initParentCategoryAutocomplete в category-form.js
             */
            initCategorySearch: function() {
                // Loading indicator за autocomplete
                let loadingIndicator = document.createElement('div');
                const categorySearchInput = document.getElementById('input-category-search');
                const categorySearchSuggestions = document.getElementById('category-search-autocomplete');

                if (!categorySearchInput || !categorySearchSuggestions) {
                    console.warn('Елементи за търсене на категории не са намерени.');
                    return;
                }

                // Проверяваме дали вече има event listeners
                if (categorySearchInput.dataset.listenerAttached === 'true') {
                    return;
                }

                loadingIndicator.className = 'autocomplete-loading p-2 text-gray-500 text-sm';
                loadingIndicator.textContent = 'Зареждане...';
                loadingIndicator.style.display = 'none';
                categorySearchSuggestions.appendChild(loadingIndicator);

                let categoryDebounceTimer;
                let currentCategoryRequest = null;

                const navigateToCategory = (categoryId) => {
                    // Навигираме към страницата за редактиране на категорията
                    const userToken = this.getUserToken();
                    const editUrl = `index.php?route=catalog/category/edit&category_id=${categoryId}&user_token=${userToken}`;
                    window.location.href = editUrl;
                };

                const fetchAndDisplayCategorySuggestions = (currentQuery) => {
                    loadingIndicator.style.display = 'block';
                    categorySearchSuggestions.classList.add('hidden');

                    if (currentCategoryRequest) {
                        currentCategoryRequest.abort();
                    }
                    const controller = new AbortController();
                    currentCategoryRequest = controller;

                    const urlParams = new URLSearchParams(window.location.search);
                    const userToken = urlParams.get('user_token');
                    const timestamp = new Date().getTime();

                    // Използваме същия endpoint като в category-form.js
                    let fetchUrl = `index.php?route=catalog/category/autocomplete&filter_name=${encodeURIComponent(currentQuery)}&user_token=${userToken}&_=${timestamp}`;

                    fetch(fetchUrl, {
                        signal: controller.signal,
                        cache: 'no-store',
                        headers: { 'Cache-Control': 'no-cache, no-store, must-revalidate', 'Pragma': 'no-cache', 'Expires': '0' }
                    })
                    .then(response => {
                        if (!response.ok) throw new Error('Грешка при зареждане на категории');
                        return response.json();
                    })
                    .then(data => {
                        loadingIndicator.style.display = 'none';

                        // Създаваме контейнер за предложенията с правилните стилове
                        categorySearchSuggestions.innerHTML = '';
                        const suggestionsList = document.createElement('div');
                        suggestionsList.className = 'absolute w-full bg-white border border-gray-300 rounded-md shadow-lg mt-1 z-10 max-h-60 overflow-y-auto';

                        if (data.length > 0) {
                            data.forEach(category => {
                                const item = document.createElement('div');
                                item.className = 'p-2 cursor-pointer hover:bg-gray-100 autocomplete-suggestion';
                                item.textContent = category.name;
                                item.dataset.id = category.category_id;
                                item.addEventListener('click', () => {
                                    navigateToCategory(category.category_id);
                                });
                                suggestionsList.appendChild(item);
                            });
                        } else {
                            const noResults = document.createElement('div');
                            noResults.className = 'p-2 text-gray-500 autocomplete-no-results';
                            noResults.textContent = currentQuery ? 'Няма намерени категории' : 'Няма предложения за категории';
                            suggestionsList.appendChild(noResults);
                        }

                        categorySearchSuggestions.appendChild(suggestionsList);
                        categorySearchSuggestions.classList.remove('hidden');
                    })
                    .catch(error => {
                        loadingIndicator.style.display = 'none';
                        if (error.name !== 'AbortError') {
                            console.error('Грешка при търсене на категории:', error);
                            categorySearchSuggestions.innerHTML = '<div class="p-2 text-red-500">Грешка при зареждане.</div>';
                            categorySearchSuggestions.classList.remove('hidden');
                        }
                    });
                };

                categorySearchInput.addEventListener('input', () => {
                    clearTimeout(categoryDebounceTimer);
                    const query = categorySearchInput.value.trim();

                    if (query.length === 0) {
                        // При празно поле показваме първите 10 категории
                        categoryDebounceTimer = setTimeout(() => {
                            fetchAndDisplayCategorySuggestions('');
                        }, 100);
                    } else {
                        // При въведен текст търсим по въведената дума
                        categoryDebounceTimer = setTimeout(() => {
                            fetchAndDisplayCategorySuggestions(query);
                        }, 500);
                    }
                });

                categorySearchInput.addEventListener('focus', () => {
                    const query = categorySearchInput.value.trim();
                    // При фокус винаги показваме предложения
                    if (query === '') {
                        // Ако полето е празно, показваме първите 10 категории
                        fetchAndDisplayCategorySuggestions('');
                    } else {
                        // Ако има текст, показваме резултати за този текст
                        fetchAndDisplayCategorySuggestions(query);
                    }
                });

                categorySearchInput.addEventListener('keydown', (e) => {
                    const suggestions = categorySearchSuggestions.querySelectorAll('.autocomplete-suggestion');
                    if (suggestions.length === 0 || categorySearchSuggestions.classList.contains('hidden')) return;

                    let activeIndex = Array.from(suggestions).findIndex(s => s.classList.contains('active'));

                    if (e.key === 'ArrowDown') {
                        e.preventDefault();
                        if (activeIndex >= 0) suggestions[activeIndex].classList.remove('active');
                        activeIndex = (activeIndex + 1) % suggestions.length;
                        suggestions[activeIndex].classList.add('active');
                        suggestions[activeIndex].scrollIntoView({ block: 'nearest' });
                    } else if (e.key === 'ArrowUp') {
                        e.preventDefault();
                        if (activeIndex >= 0) suggestions[activeIndex].classList.remove('active');
                        activeIndex = (activeIndex - 1 + suggestions.length) % suggestions.length;
                        suggestions[activeIndex].classList.add('active');
                        suggestions[activeIndex].scrollIntoView({ block: 'nearest' });
                    } else if (e.key === 'Enter' && activeIndex >= 0) {
                        e.preventDefault();
                        suggestions[activeIndex].click();
                    } else if (e.key === 'Escape') {
                        categorySearchSuggestions.innerHTML = '';
                        categorySearchSuggestions.classList.add('hidden');
                    }
                });

                document.addEventListener('click', function(event) {
                    if (!categorySearchInput.contains(event.target) && !categorySearchSuggestions.contains(event.target)) {
                        categorySearchSuggestions.classList.add('hidden');
                    }
                });

                // Маркираме, че event listeners са добавени
                categorySearchInput.dataset.listenerAttached = 'true';
            }
        });
    }
})();
